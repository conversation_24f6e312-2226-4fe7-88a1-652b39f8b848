-- section SCHEMA
CREATE SCHEMA IF NOT EXISTS app_webapp
;

GRANT USAGE ON SCHEMA app_webapp TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_webapp TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_webapp TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_webapp TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_webapp
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_webapp
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_webapp
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section